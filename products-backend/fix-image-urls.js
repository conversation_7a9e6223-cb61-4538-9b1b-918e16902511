const mongoose = require('mongoose');
require('dotenv').config();

// 定义模型Schema
const ProductSchema = new mongoose.Schema({}, { strict: false });
const ImageSchema = new mongoose.Schema({}, { strict: false });

const Product = mongoose.model('Product', ProductSchema);
const Image = mongoose.model('Image', ImageSchema);

async function fixImageUrls() {
  try {
    console.log('🚀 开始修复产品图片URL...');
    
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ 数据库连接成功');
    
    // 获取所有产品
    const products = await Product.find({}).limit(10); // 先测试10个产品
    console.log(`📦 找到 ${products.length} 个产品需要检查`);
    
    let fixedCount = 0;
    let skippedCount = 0;
    
    for (const product of products) {
      console.log(`\n🔍 检查产品: ${product.productId}`);
      
      const imageTypes = ['front', 'back', 'label', 'package', 'gift'];
      let productNeedsUpdate = false;
      const updates = {};
      
      for (const imageType of imageTypes) {
        const imageValue = product.images?.[imageType];
        
        if (imageValue && typeof imageValue === 'string') {
          // 检查是否是飞书token（不是HTTP URL且长度大于20）
          if (!imageValue.startsWith('http') && imageValue.length > 20) {
            console.log(`  🔧 修复 ${imageType} 图片: ${imageValue.substring(0, 20)}...`);
            
            // 查找对应的Image记录
            const imageRecord = await Image.findOne({
              productId: product.productId,
              type: imageType,
              'metadata.feishuFileToken': imageValue,
              isActive: true
            });
            
            if (imageRecord && imageRecord.publicUrl) {
              // 更新为MinIO URL，但需要修正为本地地址
              let fixedUrl = imageRecord.publicUrl;
              if (fixedUrl.includes('*************')) {
                fixedUrl = fixedUrl.replace('*************', 'localhost');
              }
              
              updates[`images.${imageType}`] = fixedUrl;
              productNeedsUpdate = true;
              console.log(`    ✅ 找到对应图片: ${fixedUrl}`);
            } else {
              console.log(`    ⚠️ 未找到对应的图片记录`);
            }
          } else if (imageValue.includes('*************')) {
            // 如果是远程URL，更新为本地URL
            const fixedUrl = imageValue.replace('*************', 'localhost');
            updates[`images.${imageType}`] = fixedUrl;
            productNeedsUpdate = true;
            console.log(`  🔧 更新远程URL为本地: ${imageType}`);
          }
        }
      }
      
      if (productNeedsUpdate) {
        await Product.updateOne(
          { productId: product.productId },
          { 
            $set: {
              ...updates,
              updatedAt: new Date()
            }
          }
        );
        fixedCount++;
        console.log(`  ✅ 产品更新完成`);
      } else {
        skippedCount++;
        console.log(`  ➡️ 无需更新`);
      }
    }
    
    console.log(`\n📊 修复完成统计:`);
    console.log(`  - 修复的产品: ${fixedCount}`);
    console.log(`  - 跳过的产品: ${skippedCount}`);
    console.log(`  - 总计: ${fixedCount + skippedCount}`);
    
    // 同时修复Image记录中的URL
    console.log(`\n🔧 修复Image记录中的URL...`);
    const imageUpdateResult = await Image.updateMany(
      { publicUrl: { $regex: '*************' } },
      [
        {
          $set: {
            publicUrl: {
              $replaceAll: {
                input: '$publicUrl',
                find: '*************',
                replacement: 'localhost'
              }
            },
            updatedAt: new Date()
          }
        }
      ]
    );
    
    console.log(`✅ 更新了 ${imageUpdateResult.modifiedCount} 个Image记录的URL`);
    
    // 同时修复缩略图URL
    const thumbnailUpdateResult = await Image.updateMany(
      { 'thumbnails.url': { $regex: '*************' } },
      [
        {
          $set: {
            thumbnails: {
              $map: {
                input: '$thumbnails',
                as: 'thumb',
                in: {
                  $mergeObjects: [
                    '$$thumb',
                    {
                      url: {
                        $replaceAll: {
                          input: '$$thumb.url',
                          find: '*************',
                          replacement: 'localhost'
                        }
                      }
                    }
                  ]
                }
              }
            },
            updatedAt: new Date()
          }
        }
      ]
    );
    
    console.log(`✅ 更新了 ${thumbnailUpdateResult.modifiedCount} 个Image记录的缩略图URL`);
    
  } catch (error) {
    console.error('❌ 修复过程失败:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixImageUrls();
}
